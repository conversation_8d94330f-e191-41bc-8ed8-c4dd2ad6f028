<template>
	<view class="order-card">
		<!-- 订单头部信息 -->
		<view class="order-header">
			<view class="order-info">
				<view class="order-no">{{ displayData.orderNo }}</view>
				<view class="customer-name">{{ displayData.customerName }}</view>
				<!-- 订单信息 -->
				<view class="order-details">
					<view class="detail-row time-row">
						<view class="time-item">
							<text class="detail-label">下单时间：</text>
							<text class="detail-value">{{ formatDate(displayData.orderDate) }}</text>
						</view>
						<view class="time-item">
							<text class="detail-label">发货时间：</text>
							<text class="detail-value">{{ formatDate(displayData.deliveryTime) }}</text>
						</view>
					</view>
					<view class="detail-row" v-if="displayData.requirement && displayData.requirement !== '-'">
						<text class="detail-label">要求：</text>
						<text class="detail-value">{{ displayData.requirement }}</text>
					</view>
				</view>
			</view>
			<view class="order-quantity">
				<text class="quantity-main">{{ displayData.mainQuantity }}</text>
				<text class="quantity-completed" v-if="displayData.completedQuantity">
					已完成: {{ displayData.completedQuantity }}
				</text>
			</view>
		</view>

		<!-- 轮播容器 -->
		<swiper
			class="order-swiper"
			:indicator-dots="true"
			:autoplay="false"
			:circular="false"
			indicator-color="rgba(0, 0, 0, .3)"
			indicator-active-color="#409eff"
			@change="handleSwiperChange"
		>
			<!-- 第一页：产品信息、数量、需求状态 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<!-- 产品信息 -->
					<view class="info-section">
						<view class="section-header">
							<text class="section-label">产品信息</text>
						</view>
						<view class="info-grid">
							<view class="info-item">
								<text class="info-label">产品名称</text>
								<text class="info-value">{{ displayData.productName }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">产品编码</text>
								<text class="info-value">{{ displayData.productCode }}</text>
							</view>
							<view class="info-item">
								<text class="info-label">规格</text>
								<text class="info-value">{{ displayData.productSpec }}</text>
							</view>
							<view class="info-item" v-if="displayData.specQuantity">
								<text class="info-label">规格数量</text>
								<text class="info-value">{{ displayData.specQuantity }}</text>
							</view>
						</view>
					</view>

					<!-- 数量信息 -->
					<view class="info-section">
						<view class="section-header">
							<text class="section-label">数量信息</text>
						</view>
						<view class="quantity-info">
							<view class="quantity-item">
								<text class="quantity-label">订单数量</text>
								<text class="quantity-value main-quantity">{{ displayData.mainQuantity }}</text>
							</view>
							<view class="quantity-item">
								<text class="quantity-label">已完成</text>
								<text class="quantity-value completed-quantity">{{ displayData.completedQuantity || '0' + displayData.unitName }}</text>
							</view>
						</view>
					</view>

					<!-- 需求状态 -->
					<view class="info-section">
						<view class="section-header">
							<text class="section-label">需求状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.requestStatusClass">
									{{ displayData.requestStatusText }}
								</view>
							</view>
							<view class="status-item" v-if="displayData.requestCreateTime">
								<text class="status-label">创建时间：{{ formatDate(displayData.requestCreateTime) }}</text>
							</view>
							<view class="status-item" v-if="displayData.requestConfirmTime && displayData.hasRequestStatus">
								<text class="status-label">BOM确认时间: {{ formatDate(displayData.requestConfirmTime) }}</text>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第二页：原料库存 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">库存状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.materialStatusClass">
									{{ displayData.materialStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">库存进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.materialProgressClass" :style="{ width: displayData.materialProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.materialProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 原料详情 -->
						<view class="material-list" v-if="displayData.materialList.length > 0">
							<view class="material-item" v-for="(material, index) in displayData.materialList" :key="index">
								<view class="material-info">
									<text class="material-name">{{ material.fullCode }} {{ material.name }}</text>
									<text class="material-quantity">{{ material.pendingQty || 0 }} {{ material.unitName || '' }}</text>
								</view>
								<view :class="'material-status ' + (material.shortage ? 'shortage' : 'sufficient')">
									{{ material.shortage ? ('缺料' + (material.shortageQty || 0)) : '已备齐' }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无原料信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第三页：采购 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">采购状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.procurementStatusClass">
									{{ displayData.procurementStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">采购进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.procurementProgressClass" :style="{ width: displayData.procurementProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.procurementProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 采购详情 -->
						<view class="purchase-list" v-if="displayData.purchaseList.length > 0">
							<view class="purchase-item" v-for="(purchase, index) in displayData.purchaseList" :key="index">
								<view class="purchase-info">
									<text class="purchase-time">{{ formatDate(purchase.purchaseTime) }}</text>
									<text class="purchase-material">{{ purchase.materialCode }} {{ purchase.materialName }}</text>
								</view>
								<view :class="'purchase-status ' + getItemStatusClass(purchase.purchaseStatus)">
									{{ getPurchaseItemStatusText(purchase.purchaseStatus) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无采购信息</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第四页：计划 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">计划状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.productionPlanStatusClass">
									{{ displayData.productionPlanStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">计划进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.productionPlanProgressClass" :style="{ width: displayData.productionPlanProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.productionPlanProgress }}%</text>
								</view>
							</view>
						</view>

						<!-- 计划详情 -->
						<view class="plan-info">
							<view class="plan-item">
								<text class="plan-label">车间</text>
								<text class="plan-value">{{ displayData.workshop }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">产线</text>
								<text class="plan-value">{{ displayData.productionLine }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">负责人</text>
								<text class="plan-value">{{ displayData.foreman }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">计划数量</text>
								<text class="plan-value">{{ displayData.planQuantity }}</text>
							</view>
							<view class="plan-item">
								<text class="plan-label">已完成</text>
								<text class="plan-value">{{ displayData.fulfilledQuantity }}</text>
							</view>
						</view>

						<!-- 计划安排 -->
						<view class="schedule-list" v-if="displayData.scheduleList.length > 0">
							<view class="schedule-title">计划安排</view>
							<view class="schedule-item" v-for="(schedule, index) in displayData.scheduleList" :key="index">
								<view class="schedule-info">
									<text class="schedule-stage">第{{ index + 1 }}阶段</text>
									<text class="schedule-time">{{ formatDate(schedule.startTime) }} ~ {{ formatDate(schedule.endTime) }}</text>
									<text class="schedule-quantity">计划: {{ schedule.quantity || 0 }}{{ displayData.unitName }}</text>
									<text class="schedule-fulfilled">已完成: {{ schedule.fulfilledQty || 0 }}{{ displayData.unitName }}</text>
								</view>
								<view :class="'schedule-status ' + getItemStatusClass(schedule.status)">
									{{ getScheduleStatusText(schedule.status) }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第五页：生产执行 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">生产状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.productionExecutionStatusClass">
									{{ displayData.productionExecutionStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">生产进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.productionExecutionProgressClass" :style="{ width: displayData.productionExecutionProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.productionExecutionProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已生产</text>
								<text class="status-value">{{ displayData.productionQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.productionStartTime">
								<text class="status-label">开始时间</text>
								<text class="status-value">{{ formatDate(displayData.productionStartTime) }}</text>
							</view>
							<view class="status-item" v-if="displayData.productionEndTime">
								<text class="status-label">完成时间</text>
								<text class="status-value">{{ formatDate(displayData.productionEndTime) }}</text>
							</view>
						</view>

						<!-- 生产详情 -->
						<view class="production-details" v-if="displayData.productionDetails.length > 0">
							<view class="details-title">生产详情</view>
							<view class="production-item" v-for="(item, index) in displayData.productionDetails" :key="index">
								<view class="production-info">
									<text class="production-batch">批次: {{ item.batchNo || '-' }}</text>
									<text class="production-workshop">车间: {{ item.workshop || '-' }}</text>
									<text class="production-line">产线: {{ item.productionLine || '-' }}</text>
									<text class="production-qty">数量: {{ item.quantity || 0 }}{{ displayData.unitName }}</text>
								</view>
								<view :class="'production-status ' + getItemStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无生产详情</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第六页：质检 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">质检状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.qualityInspectionStatusClass">
									{{ displayData.qualityInspectionStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">质检进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.qualityInspectionProgressClass" :style="{ width: displayData.qualityInspectionProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.qualityInspectionProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已质检</text>
								<text class="status-value">{{ displayData.qualityInspectedQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.qualityPassedQuantity">
								<text class="status-label">合格数量</text>
								<text class="status-value">{{ displayData.qualityPassedQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.qualityRejectedQuantity">
								<text class="status-label">不合格数量</text>
								<text class="status-value">{{ displayData.qualityRejectedQuantity }}</text>
							</view>
						</view>

						<!-- 质检详情 -->
						<view class="quality-details" v-if="displayData.qualityDetails.length > 0">
							<view class="details-title">质检详情</view>
							<view class="quality-item" v-for="(item, index) in displayData.qualityDetails" :key="index">
								<view class="quality-info">
									<text class="quality-batch">批次: {{ item.batchNo || '-' }}</text>
									<text class="quality-inspector">质检员: {{ item.inspector || '-' }}</text>
									<text class="quality-time">质检时间: {{ formatDate(item.inspectionTime) }}</text>
									<text class="quality-result">结果: {{ item.result || '-' }}</text>
								</view>
								<view :class="'quality-status ' + getItemStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无质检详情</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第七页：入库 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">入库状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.warehousingStatusClass">
									{{ displayData.warehousingStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">入库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.warehousingProgressClass" :style="{ width: displayData.warehousingProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.warehousingProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已入库</text>
								<text class="status-value">{{ displayData.warehouseInQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.warehouseLocation">
								<text class="status-label">仓库位置</text>
								<text class="status-value">{{ displayData.warehouseLocation }}</text>
							</view>
						</view>

						<!-- 入库详情 -->
						<view class="warehouse-details" v-if="displayData.warehouseInDetails.length > 0">
							<view class="details-title">入库详情</view>
							<view class="warehouse-item" v-for="(item, index) in displayData.warehouseInDetails" :key="index">
								<view class="warehouse-info">
									<text class="warehouse-batch">批次: {{ item.batchNo || '-' }}</text>
									<text class="warehouse-location">位置: {{ item.location || '-' }}</text>
									<text class="warehouse-time">入库时间: {{ formatDate(item.warehouseTime) }}</text>
									<text class="warehouse-qty">数量: {{ item.quantity || 0 }}{{ displayData.unitName }}</text>
								</view>
								<view :class="'warehouse-status ' + getItemStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无入库详情</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第八页：发货 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">发货状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.deliveryStatusClass">
									{{ displayData.deliveryStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">发货进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.deliveryProgressClass" :style="{ width: displayData.deliveryProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.deliveryProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已发货</text>
								<text class="status-value">{{ displayData.deliveredQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.deliveryAddress">
								<text class="status-label">发货地址</text>
								<text class="status-value">{{ displayData.deliveryAddress }}</text>
							</view>
							<view class="status-item" v-if="displayData.logisticsCompany">
								<text class="status-label">物流公司</text>
								<text class="status-value">{{ displayData.logisticsCompany }}</text>
							</view>
							<view class="status-item" v-if="displayData.trackingNumber">
								<text class="status-label">运单号</text>
								<text class="status-value">{{ displayData.trackingNumber }}</text>
							</view>
						</view>

						<!-- 发货详情 -->
						<view class="delivery-details" v-if="displayData.deliveryDetails.length > 0">
							<view class="details-title">发货详情</view>
							<view class="delivery-item" v-for="(item, index) in displayData.deliveryDetails" :key="index">
								<view class="delivery-info">
									<text class="delivery-batch">批次: {{ item.batchNo || '-' }}</text>
									<text class="delivery-time">发货时间: {{ formatDate(item.deliveryTime) }}</text>
									<text class="delivery-qty">数量: {{ item.quantity || 0 }}{{ displayData.unitName }}</text>
									<text class="delivery-tracking">运单号: {{ item.trackingNumber || '-' }}</text>
								</view>
								<view :class="'delivery-status ' + getItemStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无发货详情</text>
						</view>
					</view>
				</view>
			</swiper-item>

			<!-- 第九页：出库 -->
			<swiper-item class="swiper-item">
				<view class="swiper-content">

					<view class="info-section">
						<view class="section-header">
							<text class="section-label">出库状态</text>
						</view>
						<view class="status-info">
							<view class="status-item">
								<text class="status-label">状态</text>
								<view :class="'status-tag ' + displayData.outboundStatusClass">
									{{ displayData.outboundStatusText }}
								</view>
							</view>
							<view class="progress-item">
								<text class="progress-label">出库进度</text>
								<view class="progress-container">
									<view class="progress-bar">
										<view :class="'progress-fill ' + displayData.outboundProgressClass" :style="{ width: displayData.outboundProgress + '%' }"></view>
									</view>
									<text class="progress-text">{{ displayData.outboundProgress }}%</text>
								</view>
							</view>
							<view class="status-item">
								<text class="status-label">已出库</text>
								<text class="status-value">{{ displayData.warehouseOutQuantity }}</text>
							</view>
							<view class="status-item" v-if="displayData.outboundReason">
								<text class="status-label">出库原因</text>
								<text class="status-value">{{ displayData.outboundReason }}</text>
							</view>
						</view>

						<!-- 出库详情 -->
						<view class="outbound-details" v-if="displayData.outboundDetails.length > 0">
							<view class="details-title">出库详情</view>
							<view class="outbound-item" v-for="(item, index) in displayData.outboundDetails" :key="index">
								<view class="outbound-info">
									<text class="outbound-batch">批次: {{ item.batchNo || '-' }}</text>
									<text class="outbound-location">出库位置: {{ item.location || '-' }}</text>
									<text class="outbound-time">出库时间: {{ formatDate(item.outboundTime) }}</text>
									<text class="outbound-qty">数量: {{ item.quantity || 0 }}{{ displayData.unitName }}</text>
									<text class="outbound-operator">操作员: {{ item.operator || '-' }}</text>
								</view>
								<view :class="'outbound-status ' + getItemStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</view>
							</view>
						</view>
						<view v-else class="empty-info">
							<text>暂无出库详情</text>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>


	</view>
</template>

<script>
import { getDictOptions, getDictLabel, DICT_TYPE } from '@/utils/dict.js'

export default {
	name: 'OrderProcessItem',
	props: {
		orderData: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			currentSwiperIndex: 0,
			// 字典数据
			dictOptions: {
				commonTaskStatus: [], // 通用任务状态
				mfgRequestStatus: [], // 生产需求状态
				purchaseReqStatus: [], // 采购需求状态
				inspectStatus: [], // 质检状态
				stockStatus: [], // 库存状态
				deliveryStatus: [], // 发货状态
				logisticsStatus: [] // 物流状态
			},
			// 处理后的显示数据
			displayData: {
				// 基本信息
				orderNo: '',
				customerName: '',
				orderDate: '',
				deliveryTime: '',
				requirement: '',
				productName: '',
				productCode: '',
				productSpec: '',
				unitName: '',
				specQuantity: '',
				mainQuantity: '',
				completedQuantity: '',

				// 需求状态
				requestStatusText: '',
				requestStatusClass: '',
				requestCreateTime: '',
				requestConfirmTime: '',
				hasRequestStatus: false,

				// 原料库存
				materialStatusText: '',
				materialStatusClass: '',
				materialProgress: 0,
				materialProgressClass: '',
				materialList: [],

				// 采购
				procurementStatusText: '',
				procurementStatusClass: '',
				procurementProgress: 0,
				procurementProgressClass: '',
				purchaseList: [],

				// 生产计划
				productionPlanStatusText: '',
				productionPlanStatusClass: '',
				productionPlanProgress: 0,
				productionPlanProgressClass: '',
				workshop: '',
				productionLine: '',
				foreman: '',
				planQuantity: '',
				fulfilledQuantity: '',
				scheduleList: [],

				// 生产执行
				productionExecutionStatusText: '',
				productionExecutionStatusClass: '',
				productionExecutionProgress: 0,
				productionExecutionProgressClass: '',
				productionQuantity: '',
				productionStartTime: '',
				productionEndTime: '',
				productionDetails: [],

				// 质检
				qualityInspectionStatusText: '',
				qualityInspectionStatusClass: '',
				qualityInspectionProgress: 0,
				qualityInspectionProgressClass: '',
				qualityInspectedQuantity: '',
				qualityPassedQuantity: '',
				qualityRejectedQuantity: '',
				qualityDetails: [],

				// 入库
				warehousingStatusText: '',
				warehousingStatusClass: '',
				warehousingProgress: 0,
				warehousingProgressClass: '',
				warehouseInQuantity: '',
				warehouseLocation: '',
				warehouseInDetails: [],

				// 发货
				deliveryStatusText: '',
				deliveryStatusClass: '',
				deliveryProgress: 0,
				deliveryProgressClass: '',
				deliveredQuantity: '',
				deliveryAddress: '',
				logisticsCompany: '',
				trackingNumber: '',
				deliveryDetails: [],

				// 出库
				outboundStatusText: '',
				outboundStatusClass: '',
				outboundProgress: 0,
				outboundProgressClass: '',
				warehouseOutQuantity: '',
				outboundReason: '',
				outboundDetails: []
			}
		}
	},
	async created() {
		// 加载字典数据
		await this.loadDictData()
		// 初始化显示数据
		this.updateDisplayData()
	},
	watch: {
		orderData: {
			handler() {
				this.updateDisplayData()
			},
			deep: true,
			immediate: true
		}
	},

	methods: {
		// 加载字典数据
		async loadDictData() {
			try {
				const [
					commonTaskStatus,
					mfgRequestStatus,
					purchaseReqStatus,
					inspectStatus,
					stockStatus,
					deliveryStatus,
					logisticsStatus
				] = await Promise.all([
					getDictOptions(DICT_TYPE.COMMON_TASK_STATUS),
					getDictOptions(DICT_TYPE.MFG_REQUEST_STATUS),
					getDictOptions(DICT_TYPE.PURCHASE_REQ_STATUS),
					getDictOptions(DICT_TYPE.INSPECT_STATUS),
					getDictOptions(DICT_TYPE.STOCK_STATUS),
					getDictOptions(DICT_TYPE.DELIVERY_STATUS),
					getDictOptions(DICT_TYPE.LOGISTICS_STATUS)
				])

				this.dictOptions = {
					commonTaskStatus,
					mfgRequestStatus,
					purchaseReqStatus,
					inspectStatus,
					stockStatus,
					deliveryStatus,
					logisticsStatus
				}
			} catch (error) {
				console.error('加载字典数据失败:', error)
			}
		},

		// 更新显示数据
		updateDisplayData() {
			if (!this.orderData) return

			// 检查数据结构：如果有rawData，说明是处理过的数据；否则是原始数据
			const isProcessedData = this.orderData.rawData !== undefined
			const sourceData = isProcessedData ? this.orderData.rawData : this.orderData

			// 基本信息处理 - 兼容两种数据结构
			if (isProcessedData) {
				// 使用处理过的扁平化数据
				this.displayData.orderNo = this.orderData.orderNo || 'N/A'
				this.displayData.customerName = this.orderData.customerName || '未知客户'
				this.displayData.orderDate = this.orderData.orderDate
				this.displayData.deliveryTime = this.orderData.deliveryTime
				this.displayData.requirement = this.orderData.requirement || this.orderData.remark || '-'
				this.displayData.productName = this.orderData.productInfo || '暂无产品信息'
				this.displayData.productCode = this.orderData.productCode || '-'
				this.displayData.productSpec = this.orderData.productSpec || '-'
				this.displayData.unitName = this.orderData.unitName || ''
				this.displayData.specQuantity = ''

				const totalQuantity = this.orderData.quantity || 0
				this.displayData.mainQuantity = `${totalQuantity}${this.displayData.unitName}`

				const completedQty = this.orderData.completedQuantity || 0
				this.displayData.completedQuantity = completedQty > 0 ? `${completedQty}${this.displayData.unitName}` : ''
			} else {
				// 使用原始嵌套数据结构
				const salesOrder = sourceData.salesOrderInfo || {}
				const product = salesOrder.product || {}

				this.displayData.orderNo = salesOrder.orderNo || sourceData.orderNo || 'N/A'
				this.displayData.customerName = salesOrder.customer?.name || sourceData.customerName || '未知客户'
				this.displayData.orderDate = salesOrder.orderDate || sourceData.orderDate
				this.displayData.deliveryTime = salesOrder.deliveryTime || sourceData.deliveryTime
				this.displayData.requirement = salesOrder.requirement || salesOrder.remark || sourceData.requirement || '-'
				this.displayData.productName = product.name || sourceData.productInfo || '暂无产品信息'
				this.displayData.productCode = product.fullCode || sourceData.productCode || '-'
				this.displayData.productSpec = product.spec || sourceData.productSpec || '-'
				this.displayData.unitName = product.unitName || sourceData.unitName || ''
				this.displayData.specQuantity = product.specQuantity || ''

				const totalQuantity = product.quantity || sourceData.quantity || 0
				this.displayData.mainQuantity = `${totalQuantity}${this.displayData.unitName}`

				const completedQty = sourceData.outbound?.quantity || sourceData.completedQuantity || 0
				this.displayData.completedQuantity = completedQty > 0 ? `${completedQty}${this.displayData.unitName}` : ''
			}

			// 需求状态处理
			const request = sourceData.request || {}
			this.displayData.hasRequestStatus = request.status !== undefined && request.status !== '0'
			this.displayData.requestStatusText = getDictLabel(this.dictOptions.mfgRequestStatus, request.status) || '待处理'
			this.displayData.requestStatusClass = this.getStatusClassByValue(request.status || '0')
			this.displayData.requestCreateTime = request.createTime
			this.displayData.requestConfirmTime = request.confirmTime

			// 获取总数量用于进度计算
			const totalQuantity = isProcessedData ? this.orderData.quantity :
				(sourceData.salesOrderInfo?.product?.quantity || sourceData.quantity || 0)

			// 处理各个流程状态 - 兼容两种数据结构
			if (isProcessedData) {
				// 使用扁平化的状态数据
				this.processStatusDataFlat('material', this.orderData.materialStatus, this.orderData.materialProgress, totalQuantity)
				this.processStatusDataFlat('procurement', this.orderData.procurementStatus, this.orderData.procurementProgress, totalQuantity)
				this.processStatusDataFlat('productionPlan', this.orderData.productionPlanStatus, this.orderData.productionPlanProgress, totalQuantity)
				this.processStatusDataFlat('productionExecution', this.orderData.productionStatus, this.orderData.productionProgress, totalQuantity)
				this.processStatusDataFlat('qualityInspection', this.orderData.qualityStatus, this.orderData.qualityProgress, totalQuantity)
				this.processStatusDataFlat('warehousing', this.orderData.warehouseStatus, this.orderData.warehouseProgress, totalQuantity)
				this.processStatusDataFlat('delivery', this.orderData.deliveryStatus, this.orderData.deliveryProgress, totalQuantity)
				this.processStatusDataFlat('outbound', sourceData.outbound?.status, sourceData.outbound?.progress, totalQuantity)
			} else {
				// 使用原始嵌套数据结构
				this.processStatusData('material', sourceData.materialInventory, 'stockStatus', totalQuantity)
				this.processStatusData('procurement', sourceData.procurement, 'purchaseReqStatus', totalQuantity)
				this.processStatusData('productionPlan', sourceData.productionPlan, 'commonTaskStatus', totalQuantity)
				this.processStatusData('productionExecution', sourceData.productionExecution, 'commonTaskStatus', totalQuantity)
				this.processStatusData('qualityInspection', sourceData.qualityInspection, 'inspectStatus', totalQuantity)
				this.processStatusData('warehousing', sourceData.warehousing, 'commonTaskStatus', totalQuantity)
				this.processStatusData('delivery', sourceData.delivery, 'deliveryStatus', totalQuantity)
				this.processStatusData('outbound', sourceData.outbound, 'commonTaskStatus', totalQuantity)
			}

			// 处理详细信息
			this.processDetailData(sourceData)
		},

		// 处理状态数据的通用方法（原始嵌套数据）
		processStatusData(type, data, dictType, totalQuantity) {
			if (!data) data = {}

			const status = data.status
			const progress = Math.round(data.progress || 0)

			// 计算进度
			let calculatedProgress = progress
			if (['productionExecution', 'qualityInspection', 'warehousing', 'delivery', 'outbound'].includes(type)) {
				const currentQuantity = data.quantity || 0
				calculatedProgress = totalQuantity > 0 ? Math.round((currentQuantity / totalQuantity) * 100) : 0
			}

			// 设置状态文本和样式类
			this.displayData[type + 'StatusText'] = getDictLabel(this.dictOptions[dictType], status) || '未知'
			this.displayData[type + 'StatusClass'] = this.getStatusClassByValue(status || '0')
			this.displayData[type + 'Progress'] = calculatedProgress
			this.displayData[type + 'ProgressClass'] = calculatedProgress >= 80 ? 'progress-green' : calculatedProgress >= 60 ? 'progress-yellow' : 'progress-red'

			// 设置列表数据
			if (type === 'material') {
				this.displayData.materialList = data.rawMaterials || []
			} else if (type === 'procurement') {
				this.displayData.purchaseList = data.purchaseItems || []
			}
		},

		// 处理扁平化状态数据的方法
		processStatusDataFlat(type, status, progress, totalQuantity) {
			const normalizedProgress = Math.round(progress || 0)

			// 根据类型确定字典类型
			const dictTypeMap = {
				'material': 'stockStatus',
				'procurement': 'purchaseReqStatus',
				'productionPlan': 'commonTaskStatus',
				'productionExecution': 'commonTaskStatus',
				'qualityInspection': 'inspectStatus',
				'warehousing': 'commonTaskStatus',
				'delivery': 'deliveryStatus',
				'outbound': 'commonTaskStatus'
			}

			const dictType = dictTypeMap[type] || 'commonTaskStatus'

			// 设置状态文本和样式类
			this.displayData[type + 'StatusText'] = getDictLabel(this.dictOptions[dictType], status) || '未知'
			this.displayData[type + 'StatusClass'] = this.getStatusClassByValue(status || '0')
			this.displayData[type + 'Progress'] = normalizedProgress
			this.displayData[type + 'ProgressClass'] = normalizedProgress >= 80 ? 'progress-green' : normalizedProgress >= 60 ? 'progress-yellow' : 'progress-red'

			// 对于扁平化数据，列表数据需要从rawData中获取
			if (type === 'material' && this.orderData.rawData) {
				this.displayData.materialList = this.orderData.rawData.materialInventory?.rawMaterials || []
			} else if (type === 'procurement' && this.orderData.rawData) {
				this.displayData.purchaseList = this.orderData.rawData.procurement?.purchaseItems || []
			}
		},

		// 处理详细信息数据
		processDetailData(sourceData = null) {
			const data = sourceData || this.orderData

			const plan = data.productionPlan || {}
			this.displayData.workshop = plan.workshop || '-'
			this.displayData.productionLine = plan.productionLine || '-'
			this.displayData.foreman = plan.foreman || '-'
			this.displayData.planQuantity = `${plan.quantity || 0}${this.displayData.unitName}`
			this.displayData.fulfilledQuantity = `${plan.fulfilledQty || 0}${this.displayData.unitName}`
			this.displayData.scheduleList = plan.schedule || []

			const execution = data.productionExecution || {}
			this.displayData.productionQuantity = `${execution.quantity || 0}${this.displayData.unitName}`
			this.displayData.productionStartTime = execution.startTime
			this.displayData.productionEndTime = execution.endTime
			this.displayData.productionDetails = execution.details || []

			const quality = data.qualityInspection || {}
			this.displayData.qualityInspectedQuantity = `${quality.quantity || 0}${this.displayData.unitName}`
			const passedQty = quality.passedQuantity || 0
			const rejectedQty = quality.rejectedQuantity || 0
			this.displayData.qualityPassedQuantity = passedQty > 0 ? `${passedQty}${this.displayData.unitName}` : ''
			this.displayData.qualityRejectedQuantity = rejectedQty > 0 ? `${rejectedQty}${this.displayData.unitName}` : ''
			this.displayData.qualityDetails = quality.details || []

			const warehousing = data.warehousing || {}
			this.displayData.warehouseInQuantity = `${warehousing.quantity || 0}${this.displayData.unitName}`
			this.displayData.warehouseLocation = warehousing.location || ''
			this.displayData.warehouseInDetails = warehousing.details || []

			const delivery = data.delivery || {}
			this.displayData.deliveredQuantity = `${delivery.quantity || 0}${this.displayData.unitName}`
			this.displayData.deliveryAddress = delivery.address || ''
			this.displayData.logisticsCompany = delivery.logisticsCompany || ''
			this.displayData.trackingNumber = delivery.trackingNumber || ''
			this.displayData.deliveryDetails = delivery.details || []

			const outbound = data.outbound || {}
			this.displayData.warehouseOutQuantity = `${outbound.quantity || 0}${this.displayData.unitName}`
			this.displayData.outboundReason = outbound.reason || ''
			this.displayData.outboundDetails = outbound.details || []
		},

		// 获取状态类名的辅助方法
		getItemStatusClass(status) {
			const statusMap = {
				'0': 'status-pending',
				'1': 'status-processing',
				'2': 'status-paused',
				'3': 'status-completed',
				'4': 'status-cancelled'
			}
			return statusMap[status] || 'status-pending'
		},

		// 轮播切换事件
		handleSwiperChange(e) {
			this.currentSwiperIndex = e.detail.current
		},

		// 格式化日期
		formatDate(date) {
			if (!date) return '-'
			const d = new Date(date)
			return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
		},





		// ===== 通用状态处理方法（使用字典数据） =====
		getStatusText(status) {
			return getDictLabel(this.dictOptions.commonTaskStatus, status) || '未知'
		},

		getStatusClass(status) {
			return this.getStatusClassByValue(status)
		},

		getStatusClassByValue(status) {
			// 根据状态值返回对应的CSS类
			const classMap = {
				'0': 'status-pending',    // 待开始
				'1': 'status-processing', // 进行中
				'2': 'status-paused',     // 已暂停
				'3': 'status-completed',  // 已完成
				'4': 'status-cancelled'   // 已取消
			}
			return classMap[status] || 'status-pending'
		},

		// ===== 采购状态方法（使用字典数据） =====
		getPurchaseItemStatusText(status) {
			return getDictLabel(this.dictOptions.purchaseReqStatus, status) || '未知'
		},

		getPurchaseItemStatusClass(status) {
			return this.getStatusClassByValue(status)
		},

		// ===== 计划安排状态方法（使用字典数据） =====
		getScheduleStatusText(status) {
			return getDictLabel(this.dictOptions.commonTaskStatus, status) || '未知'
		},

		getScheduleStatusClass(status) {
			return this.getStatusClassByValue(status)
		}
	}
}
</script>

<style scoped>
.order-card {
	background: white;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.2s;
}

.order-card:active {
	transform: scale(0.98);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
}

.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.order-no {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 4px;
}

.customer-name {
	font-size: 14px;
	color: #409eff;
	font-weight: 500;
	margin-bottom: 6px;
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.detail-row {
	display: flex;
	align-items: center;
	gap: 4px;
}

.time-row {
	display: flex;
	justify-content: space-between;
	gap: 8px;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 4px;
	flex: 1;
}

.detail-label {
	font-size: 12px;
	color: #666;
	min-width: 60px;
}

.detail-value {
	font-size: 12px;
	color: #333;
	flex: 1;
}





.order-quantity {
	text-align: right;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.quantity-main {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.quantity-completed {
	font-size: 12px;
	color: #67c23a;
}

/* 轮播容器样式 */
.order-swiper {
	margin-bottom: 16px;
}

.swiper-item {
	padding: 0 4px;
}

.swiper-content {
	padding-bottom: 10px;
}



/* 信息区块样式 */
.info-section {
	margin-bottom: 12px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 8px;
	min-height: auto;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 6px;
	margin-bottom: 8px;
}

.section-label {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 8px;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.info-label {
	font-size: 12px;
	color: #666;
}

.info-value {
	font-size: 13px;
	color: #333;
	word-break: break-all;
}

/* 状态信息样式 */
.status-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

/* 数量信息样式 */
.quantity-info {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.quantity-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.quantity-label {
	font-size: 12px;
	color: #666;
}

.quantity-value {
	font-size: 14px;
	font-weight: 500;
}

.quantity-value.main-quantity {
	color: #409eff;
}

.quantity-value.completed-quantity {
	color: #67c23a;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status-label {
	font-size: 12px;
	color: #666;
}

.status-value {
	font-size: 13px;
	color: #333;
}

.status-tag {
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 11px;
	font-weight: 500;
}

.status-pending {
	background-color: #f0f0f0;
	color: #666;
}

.status-confirmed {
	background-color: #e3f2fd;
	color: #1976d2;
}

.status-processing {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-completed {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-shortage {
	background-color: #ffebee;
	color: #f44336;
}

.status-sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

.status-partial {
	background-color: #fff3e0;
	color: #f57c00;
}

.status-exceeded {
	background-color: #e1f5fe;
	color: #0288d1;
}

.status-paused {
	background-color: #fafafa;
	color: #757575;
}

.status-cancelled {
	background-color: #ffebee;
	color: #f44336;
}

.status-paused {
	background-color: #fafafa;
	color: #757575;
}

/* 进度条样式 */
.progress-item {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.progress-label {
	font-size: 12px;
	color: #666;
}

.progress-container {
	display: flex;
	align-items: center;
	gap: 8px;
}

.progress-bar {
	flex: 1;
	height: 6px;
	background-color: #e4e7ed;
	border-radius: 3px;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	border-radius: 3px;
	transition: width 0.3s ease;
}

.progress-green {
	background-color: #67c23a;
}

.progress-yellow {
	background-color: #e6a23c;
}

.progress-red {
	background-color: #f56c6c;
}

.progress-text {
	font-size: 11px;
	color: #666;
	min-width: 30px;
	text-align: right;
}

/* 原料列表样式 */
.material-list {
	margin-top: 8px;
	max-height: 200px;
	overflow-y: auto;
}

.material-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #eee;
}

.material-item:last-child {
	border-bottom: none;
}

.material-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.material-name {
	font-size: 12px;
	color: #333;
}

.material-quantity {
	font-size: 11px;
	color: #666;
}

.material-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

.material-status.shortage {
	background-color: #ffebee;
	color: #f44336;
}

.material-status.sufficient {
	background-color: #e8f5e8;
	color: #4caf50;
}

/* 采购列表样式 */
.purchase-list {
	margin-top: 8px;
	max-height: 200px;
	overflow-y: auto;
}

.purchase-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
	border-bottom: 1px solid #eee;
}

.purchase-item:last-child {
	border-bottom: none;
}

.purchase-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.purchase-time {
	font-size: 11px;
	color: #666;
}

.purchase-material {
	font-size: 12px;
	color: #333;
}

.purchase-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
}

/* 计划信息样式 */
.plan-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	margin-top: 8px;
}

.plan-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.plan-label {
	font-size: 12px;
	color: #666;
}

.plan-value {
	font-size: 12px;
	color: #333;
}

/* 计划安排样式 */
.schedule-list {
	margin-top: 12px;
	max-height: 250px;
	overflow-y: auto;
}

.schedule-title {
	font-size: 13px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
}

.schedule-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 10px 0;
	border-bottom: 1px solid #eee;
}

.schedule-item:last-child {
	border-bottom: none;
}

.schedule-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.schedule-stage {
	font-size: 12px;
	font-weight: 500;
	color: #333;
}

.schedule-time, .schedule-quantity, .schedule-fulfilled {
	font-size: 11px;
	color: #666;
}

.schedule-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
	margin-left: 8px;
}

/* 空信息样式 */
.empty-info {
	text-align: center;
	padding: 15px;
	color: #999;
	font-size: 12px;
	background: #fafafa;
	border-radius: 6px;
	margin-top: 8px;
}



/* 轮播指示器自定义样式 */
.order-swiper ::v-deep .uni-swiper-dots {
	bottom: 8px;
}

.order-swiper ::v-deep .uni-swiper-dot {
	width: 6px;
	height: 6px;
	margin: 0 3px;
}

.order-swiper ::v-deep .uni-swiper-dot-active {
	background-color: #409eff;
}

/* 详情列表通用样式 */
.details-title {
	font-size: 12px;
	font-weight: 500;
	color: #333;
	margin: 8px 0 6px 0;
	padding-bottom: 2px;
	border-bottom: 1px solid #eee;
}

.production-details, .quality-details, .warehouse-details, .delivery-details, .outbound-details {
	margin-top: 8px;
	max-height: 200px;
	overflow-y: auto;
}

.production-item, .quality-item, .warehouse-item, .delivery-item, .outbound-item {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 8px 0;
	border-bottom: 1px solid #f0f0f0;
}

.production-item:last-child, .quality-item:last-child, .warehouse-item:last-child,
.delivery-item:last-child, .outbound-item:last-child {
	border-bottom: none;
}

.production-info, .quality-info, .warehouse-info, .delivery-info, .outbound-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.production-info text, .quality-info text, .warehouse-info text, .delivery-info text, .outbound-info text {
	font-size: 11px;
	color: #666;
}

.production-status, .quality-status, .warehouse-status, .delivery-status, .outbound-status {
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 500;
	margin-left: 8px;
}

/* 响应式调整 */
@media (max-width: 375px) {
	.info-grid {
		grid-template-columns: 1fr;
	}

	.info-section {
		margin-bottom: 10px;
		padding: 10px;
	}

	.production-details, .quality-details, .warehouse-details, .delivery-details, .outbound-details {
		max-height: 200px;
	}
}
</style>
